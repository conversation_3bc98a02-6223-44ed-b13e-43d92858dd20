import ServiceCard from '../ui/Cards/ServiceCard';
import { 
  WebDesignIcon, 
  ECommerceIcon, 
  VisualIdentityIcon, 
  MobileAppIcon, 
  ProjectManagementIcon 
} from '../ui/Icons/ServiceIcons';

const services = [
  {
    id: 1,
    title: 'Web Design',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <WebDesignIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'blue' as const
  },
  {
    id: 2,
    title: 'UI/UX Design',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <VisualIdentityIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'dark' as const
  },
  {
    id: 3,
    title: 'Visual Identity',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <VisualIdentityIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'black' as const
  },
  {
    id: 4,
    title: 'E-Commerce',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <ECommerceIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'purple' as const
  },
  {
    id: 5,
    title: 'Mobile Applications',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <MobileAppIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'blue' as const
  },
  {
    id: 6,
    title: 'Digital Project Management',
    description: 'help you to build website company that is modern, user friendly, good CEO, and Clean design',
    icon: <ProjectManagementIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />,
    variant: 'dark' as const
  }
];

export default function ServicesGrid() {
  return (
    <section className=" mt-7">

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6 lg:gap-8 max-w-7xl mx-auto">
        {services.map((service, index) => (
          <ServiceCard
            key={service.id}
            title={service.title}
            description={service.description}
            icon={service.icon}
            variant={service.variant}
            className={`
              ${index === 0 ? 'md:col-span-1' : ''}
              ${index === 1 ? 'md:col-span-1' : ''}
              ${index === 2 ? 'md:col-span-1' : ''}
              ${index === 3 ? 'md:col-span-1' : ''}
              min-h-[280px] lg:min-h-[320px]
            `}
          />
        ))}
      </div>
    </section>
  );
}
