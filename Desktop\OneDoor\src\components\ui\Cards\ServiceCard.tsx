import { ReactNode } from 'react';

interface ServiceCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  variant: 'blue' | 'dark' | 'purple' | 'black';
  className?: string;
}

export default function ServiceCard({ title, description, icon, variant, className = '' }: ServiceCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'blue':
        return 'bg-gradient-to-br from-[#5B5FFF] to-[#4A4EFF] text-white';
      case 'dark':
        return 'bg-gradient-to-br from-[#2D2D2D] to-[#1A1A1A] text-white service-card-dark';
      case 'purple':
        return 'bg-gradient-to-br from-[#A43CA6] to-[#722973] text-white';
      case 'black':
        return 'bg-gradient-to-br from-[#1A1A1A] to-[#000000] text-white service-card-black';
      default:
        return 'bg-gradient-to-br from-[#5B5FFF] to-[#4A4EFF] text-white';
    }
  };

  return (
    <div className={`
      ${getVariantStyles()}
      rounded-2xl p-6 lg:p-8 
      relative overflow-hidden 
      transition-all duration-300 
      hover:scale-105 hover:shadow-2xl
      group cursor-pointer
      ${className}
    `}>
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10 transform translate-x-8 -translate-y-8">
        <div className="w-full h-full rounded-full bg-white/20"></div>
      </div>
      <div className="absolute bottom-0 left-0 w-24 h-24 opacity-10 transform -translate-x-6 translate-y-6">
        <div className="w-full h-full rounded-full bg-white/20"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Icon */}
        <div className="mb-6 w-12 h-12 lg:w-16 lg:h-16 rounded-full bg-white/20 flex items-center justify-center backdrop-blur-sm">
          {icon}
        </div>

        {/* Title */}
        <h3 className="text-xl lg:text-2xl font-bold mb-4 group-hover:text-white/90 transition-colors">
          {title}
        </h3>

        {/* Description */}
        <p className="text-sm lg:text-base opacity-90 mb-6 flex-grow leading-relaxed">
          {description}
        </p>

        {/* Call to action */}
        <div className="flex items-center text-sm lg:text-base font-semibold group-hover:translate-x-2 transition-transform">
          <span className="underline">Start with us</span>
          <svg 
            className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        </div>
      </div>
    </div>
  );
}
