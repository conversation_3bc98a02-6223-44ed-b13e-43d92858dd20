import icon from '../../assets/up.png'
import icone from '../../assets/uil_arrow-up.png'
import global from '../../assets/global.png'
import web from '../../assets/web.png'
import ServicesGrid from '../ServicesGrid/ServicesGrid'

export default function ServicesSection() {
  return (
    <>
      <section className="relative px-6 md:px-10 overflow-hidden lg:px-17 py-16 lg:py-30">
        {/* Responsive Decorative Line */}
        <div className="absolute z-10 pointer-events-none
                       left-1 top-16 w-16 h-20
                       sm:left-2 sm:top-20 sm:w-20 sm:h-24
                       md:left-4 md:top-28 md:w-24 md:h-32
                       lg:left-6 lg:top-36 lg:w-32 lg:h-40
                       xl:left-8 xl:top-44 xl:w-40 xl:h-48
                       opacity-60 lg:opacity-80">
          <svg
            viewBox="0 0 200 300"
            className="w-full h-full"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="decorativeLine" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#01002B" />
                <stop offset="3%" stopColor="#A43CA6" />
                <stop offset="96%" stopColor="#EE05F2" />
                <stop offset="100%" stopColor="#01002B" />
              </linearGradient>
            </defs>
            <path
              d="M20 290 C20 290, 20 200, 20 150 C20 100, 20 80, 50 80 L150 80 C180 80, 180 50, 180 20 L180 10"
              stroke="url(#decorativeLine)"
              strokeWidth="3"
              fill="none"
              opacity="0.8"
              className="drop-shadow-sm"
            />
          </svg>
        </div>

        {/* Main heading section */}
        <div className="flex flex-col lg:flex-row gap-5 items-start lg:items-center mb-8">
          <p className="text-3xl md:text-5xl lg:text-[69px] leading-tight lg:leading-24 font-bold bg-gradient-to-r from-[#722973] to-[#cd5fcf] bg-clip-text text-transparent">
            Company that can help you in
          </p>
          <div className="bg-black absolute z-20 w-[200px]  right-20 dark:bg-white mt-2 flex items-center justify-center rounded-full h-[50px] lg:flex-1 px-6 lg:px-0 text-white dark:text-black">
            <button className="text-base font-medium flex items-center justify-center gap-2.5">
              Know More
              <img src={icon} alt="" className='w-5 h-5 dark:invert' />
            </button>
          </div>
        </div>

        <p className="text-3xl md:text-5xl lg:text-[64px] leading-tight lg:leading-20 font-bold bg-gradient-to-r to-[#722973] from-[#cd5fcf] bg-clip-text text-transparent mb-8">
          developing your company for the future
        </p>

        <p className='my-8 w-full lg:w-2/5 font-medium text-gray-700 dark:text-gray-300'>
          Work in the IT field includes Web Design, E-commerce, Branding, Mobile Applications, and Digital Project Management.
        </p>
        {/* Let's work together section */}
        <div className="mt-16 lg:mt-32">
          <p className="text-3xl md:text-5xl lg:text-[69px] leading-tight lg:leading-24 font-bold bg-gradient-to-r from-[#722973] to-[#cd5fcf] bg-clip-text text-transparent">
            Let's work together
          </p>
          <p className="text-3xl md:text-5xl lg:text-[69px] leading-tight lg:leading-24 font-bold bg-gradient-to-r from-[#722973] to-[#cd5fcf] bg-clip-text text-transparent">
            with us
          </p>

          <div className='flex flex-col lg:flex-row w-full justify-between items-start lg:items-center gap-6 lg:gap-0 mt-8'>
            <p className='w-full lg:w-[40%] font-medium text-gray-700 dark:text-gray-300'>
              help you to build website company that is modern, user friendly, good CEO, and Clean design
            </p>
            <div className='bg-gradient-to-r from-[#722973] to-[#a43ca6] text-white rounded-full py-3.5 px-6 flex items-center justify-center gap-2.5 hover:shadow-lg transition-shadow'>
              <button className='text-base font-medium'>Get Started</button>
              <img src={icone} alt="" />
            </div>
          </div>

          {/* Website Dev Card */}
{/*           <div className='relative mt-10 w-full lg:w-3/5 lg:ml-6.5 z-50 flex bg-black dark:bg-gray-800 rounded-2xl p-6 lg:p-10 overflow-hidden'>
            <div className='w-full lg:w-2/5 text-white'>
              <div className='mb-6 w-15 h-15 rounded-full bg-[#BE80BF] border-l-2 flex justify-center items-center border-white'>
                <img src={global} alt="" />
              </div>
              <h3 className='text-xl font-medium mb-4'>Website Dev</h3>
              <p className='mb-4 text-sm lg:text-base'>help you to build website company that is modern, user friendly, good CEO, and Clean design</p>
              <p className='font-semibold text-lg underline cursor-pointer hover:text-gray-300 transition-colors'>Start with us</p>
            </div>
            <div className="hidden lg:block">
              <img src={web} alt="" className='absolute -bottom-5 -right-5' />
            </div>
          </div> */}
          <ServicesGrid/>
        </div>
      </section>

    </>
  )
}
