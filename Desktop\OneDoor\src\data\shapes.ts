import fram from '../assets/hexagon.svg'
import fram2 from '../assets/hexagon-dark.svg'
import fram3 from '../assets/hexagon-light.svg'
import boint from '../assets/circle.svg'

export const shapes = [
  { src: fram, className: 'lg:w-25 md:w-20 w-10 absolute lg:top-20 md:top-18 top-15 right-0', type: 'hexagon' },
  { src: boint, className: 'w-3 absolute lg:top-40 md:top-38 top-35 lg:right-30 md:right-25 right-15', type: 'circle' },
  { src: boint, className: 'opacity-15 w-3 absolute lg:top-60 md:top-58 top-50 lg:right-20 md:right-15 right-10', type: 'circle' },
  { src: boint, className: 'opacity-55 w-3 absolute top-10 lg:right-10 md:right-5 right-0', type: 'circle' },
  { src: boint, className: ' w-2 absolute lg:top-40 md:top-38 top-35  lg:right-60 md:right-50 right-40', type: 'circle' },
  { src: boint, className: ' w-2 absolute lg:bottom-40 md:bottom-38 bottom-35  lg:right-60 md:right-50 right-40', type: 'circle' },
  { src: boint, className: ' w-2 absolute bottom-4 lg:right-10 md:right-5 right-0', type: 'circle' },
  { src: fram2, className: 'lg:w-35 md:w-30 w-25 absolute lg:top-20 md:top-19 top-18 lg:right-13 md:right-5 right-0', type: 'hexagon' },
  { src: fram3, className: 'lg:w-30 md:w-25 w-15 absolute lg:top-40 md:top-38 top-35 lg:right-5 right-0', type: 'hexagon' },
  { src: fram3, className: 'lg:w-50 md:w-40 w-35 opacity-35 absolute -top-5 lg:right-25 md:right-10 -right-7', type: 'hexagon' },
  { src: fram3, className: 'lg:w-55  md:w-45 w-40 absolute -bottom-5 -right-10 opacity-20', type: 'hexagon' },
  { src: fram2, className: 'lg:w-55 md:block  hidden md:w-45 w-40 absolute lg:bottom-7 md:bottom-5 bottom-2 lg:right-20 md:right-15 right-10 opacity-20', type: 'hexagon' },
  { src: boint, className: 'w-3  absolute lg:bottom-40 md:bottom-30 bottom-20 lg:right-30 md:right-25 right-20', type: 'circle' },
  { src: fram, className: 'lg:w-30 md:w-25 w-15 absolute bottom-0 left-0', type: 'hexagon' },
  { src: fram, className: 'lg:w-40 md:w-35 w-25 absolute lg:bottom-60 md:bottom-50 bottom-40 -left-10', type: 'hexagon' },
  { src: fram, className: 'lg:w-40 md:w-35 w-25 opacity-35 absolute lg:bottom-80 bottom-120  lg:left-5 md:left-2 -left-5', type: 'hexagon' },
  { src: fram2, className: 'lg:w-30 md:w-25 w-15 md:block  hidden absolute bottom-0 lg:left-15 md:left-10 left-5', type: 'hexagon' },
  { src: fram3, className: 'lg:w-25 md:w-20 w-10 absolute bottom-10 left-0', type: 'hexagon' },
  { src: fram2, className: 'lg:w-30 md:w-25 w-20  absolute bottom-0 right-15', type: 'hexagon' },
  { src: fram2, className: 'lg:w-30 md:w-25 w-20 absolute bottom-35 lg:right-15 md:right-10 right-5', type: 'hexagon' },
  { src: fram3, className: 'lg:w-25 md:w-20 w-10 absolute bottom-45 lg:right-30 md:right-20 right-0', type: 'hexagon' },
  { src: fram3, className: 'lg:w-25 md:w-20 w-10 absolute lg:bottom-10 md:bottom-35 bottom-25 right-0', type: 'hexagon' },
  { src: fram3, className: 'lg:w-35 md:w-30 w-25  absolute lg:bottom-45 bottom-30 lg:left-10 md:left-5 -left-5', type: 'hexagon' },
  { src: boint, className: 'w-3 absolute top-80 lg:left-30 md:left-20 left-10', type: 'circle' },
  { src: boint, className: 'w-3 md:block  hidden absolute top-70 lg:left-10 md:left-5 left-0', type: 'circle' },
  { src: boint, className: 'opacity-40 w-3 absolute bottom-20 lg:left-10 md:left-5 left-0', type: 'circle' },
  { src: boint, className: 'w-3 md:block  hidden absolute top-30 left-20', type: 'circle' },
  { src: fram, className: 'lg:w-25 md:w-20 w-10 absolute lg:bottom-100 md:bottom-110 bottom-125 left-0', type: 'hexagon' },
  { src: fram3, className: 'opacity-55 lg:w-35 md:w-30 w-25  absolute lg:bottom-78 md:bottom-80 bottom-100 -left-10', type: 'hexagon' }
]
