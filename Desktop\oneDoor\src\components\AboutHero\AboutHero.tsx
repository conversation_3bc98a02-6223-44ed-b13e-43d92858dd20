
export default function AboutHero() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 overflow-hidden">
      {/* Background Geometric Elements - Inspired by the image */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Top Left Corner Elements */}
        <div className="absolute top-8 left-8 opacity-20">
          <svg width="120" height="120" viewBox="0 0 120 120" className="text-purple-300">
            <rect x="10" y="10" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
            <rect x="40" y="10" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
            <rect x="10" y="40" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
            <rect x="40" y="40" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
            <circle cx="85" cy="25" r="8" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="85" cy="55" r="8" fill="none" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>

        {/* Top Right Corner Elements */}
        <div className="absolute top-12 right-12 opacity-15">
          <svg width="150" height="150" viewBox="0 0 150 150" className="text-pink-300">
            <path d="M20 20 L50 20 L50 35 L35 35 L35 50 L20 50 Z" fill="none" stroke="currentColor" strokeWidth="2"/>
            <path d="M70 20 L100 20 L100 35 L85 35 L85 50 L70 50 Z" fill="none" stroke="currentColor" strokeWidth="2"/>
            <path d="M20 70 L50 70 L50 85 L35 85 L35 100 L20 100 Z" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="120" cy="40" r="12" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="120" cy="80" r="12" fill="none" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>

        {/* Bottom Left Corner Elements */}
        <div className="absolute bottom-16 left-16 opacity-25">
          <svg width="100" height="100" viewBox="0 0 100 100" className="text-indigo-300">
            <rect x="10" y="10" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
            <rect x="35" y="10" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
            <rect x="60" y="10" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
            <rect x="10" y="35" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
            <rect x="35" y="35" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
            <rect x="10" y="60" width="15" height="15" fill="none" stroke="currentColor" strokeWidth="2" rx="3"/>
          </svg>
        </div>

        {/* Bottom Right Corner Elements */}
        <div className="absolute bottom-12 right-20 opacity-20">
          <svg width="130" height="130" viewBox="0 0 130 130" className="text-purple-400">
            <circle cx="25" cy="25" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="55" cy="25" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="85" cy="25" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="25" cy="55" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="55" cy="55" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="25" cy="85" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
            <rect x="100" y="15" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
            <rect x="100" y="45" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" rx="4"/>
          </svg>
        </div>

        {/* Center Floating Elements */}
        <div className="absolute top-1/4 left-1/4 opacity-10">
          <svg width="80" height="80" viewBox="0 0 80 80" className="text-white animate-pulse">
            <polygon points="40,5 70,25 70,55 40,75 10,55 10,25" fill="none" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>

        <div className="absolute top-3/4 right-1/3 opacity-15">
          <svg width="60" height="60" viewBox="0 0 60 60" className="text-pink-200 animate-pulse">
            <circle cx="30" cy="30" r="20" fill="none" stroke="currentColor" strokeWidth="2"/>
            <circle cx="30" cy="30" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-6 lg:px-8 flex items-center min-h-screen">
        <div className="max-w-4xl mx-auto text-center text-white">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-5xl lg:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
              من نحن
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full"></div>
          </div>

          {/* Main Description */}
          <div className="space-y-8 text-lg lg:text-xl leading-relaxed">
            <p className="text-purple-100">
              نحن في <span className="font-bold text-white">ONE DOOR</span> نؤمن بأن الابتكار الرقمي هو مفتاح التحول والنمو لأي عمل.
              نعمل بشغف لتصميم وتطوير حلول تكنولوجية متكاملة — من مواقع إلكترونية وتطبيقات إلى منصّات التجارة الإلكترونية —
              لتلبية احتياجات عملائنا وتحويل أفكارهم إلى واقع ملموس.
            </p>

            <p className="text-purple-100">
              من خلال الدمج بين الإبداع والتكنولوجيا، نحرص على:
            </p>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                  <h3 className="font-semibold text-white">واجهات مستخدم سلسة وجذابة</h3>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-pink-400 rounded-full mr-3"></div>
                  <h3 className="font-semibold text-white">ضمان أداء مستقر وآمن</h3>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-indigo-400 rounded-full mr-3"></div>
                  <h3 className="font-semibold text-white">تسهيل تجربة العميل النهائي</h3>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-center mb-3">
                  <div className="w-3 h-3 bg-purple-300 rounded-full mr-3"></div>
                  <h3 className="font-semibold text-white">الاعتماد على أحدث الأدوات والمنصّات الرقمية</h3>
                </div>
              </div>
            </div>

            {/* Mission Statement */}
            <div className="bg-gradient-to-r from-purple-600/30 to-pink-600/30 backdrop-blur-sm rounded-3xl p-8 border border-white/20 mt-12">
              <p className="text-white text-xl leading-relaxed">
                هدفنا أن نكون شريكًا فعليًا للعملاء في رحلة التطور الرقمي: نبدأ بفهم رؤيتك وأهدافك،
                ثم نصمم ونطوّر ونقدّم الدعم اللازم لتحقيقها. نؤمن بأن النجاح المستدام يأتينا من بناء علاقات
                تقوم على <span className="font-bold text-purple-200">الثقة</span>،
                <span className="font-bold text-pink-200"> الجودة</span>،
                و<span className="font-bold text-indigo-200">الشفافية</span>.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
