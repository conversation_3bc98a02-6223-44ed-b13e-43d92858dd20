<svg width="334" height="312" viewBox="0 0 334 312" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-14.75" y="-14.75" width="493.5" height="369.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_0_130_668_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="15" d="M1 49C1 26.3726 1 15.0589 8.02944 8.02944C15.0589 1 26.3726 1 49 1H415C437.627 1 448.941 1 455.971 8.02944C463 15.0589 463 26.3726 463 49V291C463 313.627 463 324.941 455.971 331.971C448.941 339 437.627 339 415 339H49C26.3726 339 15.0589 339 8.02944 331.971C1 324.941 1 313.627 1 291V49Z" fill="url(#paint0_linear_130_668)" fill-opacity="0.4" stroke="url(#paint1_linear_130_668)" stroke-width="1.5"/>
<path d="M7.50195 44L379.502 44" stroke="white" stroke-opacity="0.1"/>
<g filter="url(#filter1_d_130_668)">
<circle cx="37.4543" cy="22.9524" r="5.95238" fill="white" fill-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_130_668)">
<circle cx="56.5017" cy="22.9524" r="5.95238" fill="white" fill-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter3_d_130_668)">
<circle cx="75.5495" cy="22.9524" r="5.95238" fill="white" fill-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<foreignObject x="85" y="-2" width="308" height="50"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_1_130_668_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="15" x="100" y="13" width="278" height="20" rx="10" fill="white" fill-opacity="0.2"/>
<path d="M110.43 28.7676C110.515 28.7676 110.654 28.7356 110.787 28.661C113.82 26.9605 114.859 26.2409 114.859 24.2953V20.2175C114.859 19.6578 114.619 19.4819 114.166 19.29C113.537 19.0288 111.506 18.2985 110.877 18.08C110.733 18.032 110.579 18 110.43 18C110.28 18 110.126 18.0426 109.987 18.08C109.358 18.2612 107.322 19.0341 106.693 19.29C106.245 19.4765 106 19.6578 106 20.2175V24.2953C106 26.2409 107.045 26.9552 110.072 28.661C110.211 28.7356 110.344 28.7676 110.43 28.7676ZM110.643 18.9062C111.448 19.226 113.01 19.791 113.836 20.0736C113.98 20.1269 114.012 20.2015 114.012 20.3827V24.0981C114.012 25.7452 113.223 26.177 110.76 27.6748C110.606 27.7708 110.52 27.7974 110.435 27.8028V18.8582C110.488 18.8582 110.558 18.8742 110.643 18.9062Z" fill="white"/>
<path d="M197.79 26L196.51 22.088H197.286L197.934 24.12C198.022 24.408 198.102 24.704 198.174 25.048C198.238 24.704 198.342 24.376 198.43 24.12L199.086 22.088H199.854L200.502 24.12C200.566 24.312 200.694 24.768 200.758 25.048C200.822 24.736 200.894 24.464 201.006 24.12L201.662 22.088H202.446L201.11 26H200.398L199.718 23.904C199.59 23.512 199.51 23.208 199.47 22.992C199.43 23.184 199.358 23.44 199.214 23.912L198.534 26H197.79ZM203.931 26L202.651 22.088H203.427L204.075 24.12C204.163 24.408 204.243 24.704 204.315 25.048C204.379 24.704 204.483 24.376 204.571 24.12L205.227 22.088H205.995L206.643 24.12C206.707 24.312 206.835 24.768 206.899 25.048C206.963 24.736 207.035 24.464 207.147 24.12L207.803 22.088H208.587L207.251 26H206.539L205.859 23.904C205.731 23.512 205.651 23.208 205.611 22.992C205.571 23.184 205.499 23.44 205.355 23.912L204.675 26H203.931ZM210.072 26L208.792 22.088H209.568L210.216 24.12C210.304 24.408 210.384 24.704 210.456 25.048C210.52 24.704 210.624 24.376 210.712 24.12L211.368 22.088H212.136L212.784 24.12C212.848 24.312 212.976 24.768 213.04 25.048C213.104 24.736 213.176 24.464 213.288 24.12L213.944 22.088H214.728L213.391 26H212.68L212 23.904C211.872 23.512 211.792 23.208 211.752 22.992C211.712 23.184 211.64 23.44 211.496 23.912L210.816 26H210.072ZM215.299 26.096C214.987 26.096 214.723 25.84 214.723 25.536C214.723 25.224 214.987 24.968 215.299 24.968C215.603 24.968 215.867 25.224 215.867 25.536C215.867 25.84 215.603 26.096 215.299 26.096ZM217.63 26H216.95V20.112H217.702V22.76C217.958 22.248 218.486 21.976 219.126 21.976C220.246 21.976 220.918 22.856 220.918 24.064C220.918 25.248 220.214 26.096 219.078 26.096C218.462 26.096 217.942 25.824 217.686 25.288L217.63 26ZM217.71 24.032C217.71 24.848 218.174 25.416 218.942 25.416C219.71 25.416 220.166 24.84 220.166 24.032C220.166 23.232 219.71 22.656 218.942 22.656C218.174 22.656 217.71 23.232 217.71 24.032ZM222.891 26.096C222.051 26.096 221.555 25.624 221.555 24.912C221.555 24.192 222.091 23.744 223.011 23.672L224.243 23.576V23.464C224.243 22.808 223.851 22.576 223.323 22.576C222.691 22.576 222.331 22.856 222.331 23.328H221.675C221.675 22.512 222.347 21.976 223.355 21.976C224.323 21.976 224.979 22.488 224.979 23.544V26H224.339L224.259 25.368C224.059 25.816 223.523 26.096 222.891 26.096ZM223.107 25.512C223.819 25.512 224.251 25.048 224.251 24.312V24.104L223.251 24.184C222.587 24.248 222.315 24.504 222.315 24.888C222.315 25.304 222.619 25.512 223.107 25.512ZM226.773 26H226.021V22.088H226.701L226.781 22.688C227.029 22.24 227.525 21.976 228.077 21.976C229.109 21.976 229.581 22.608 229.581 23.608V26H228.829V23.776C228.829 22.984 228.461 22.664 227.885 22.664C227.181 22.664 226.773 23.168 226.773 23.928V26ZM231.726 26H230.974V22.72H230.206V22.088H230.974V20.864H231.726V22.088H232.494V22.72H231.726V26ZM235.873 22.088H236.617V26H235.945L235.865 25.408C235.641 25.816 235.121 26.096 234.545 26.096C233.633 26.096 233.121 25.48 233.121 24.52V22.088H233.873V24.272C233.873 25.112 234.233 25.432 234.809 25.432C235.497 25.432 235.873 24.992 235.873 24.152V22.088ZM238.268 26.096C237.956 26.096 237.692 25.84 237.692 25.536C237.692 25.224 237.956 24.968 238.268 24.968C238.572 24.968 238.836 25.224 238.836 25.536C238.836 25.84 238.572 26.096 238.268 26.096ZM240.985 26.096C240.145 26.096 239.649 25.624 239.649 24.912C239.649 24.192 240.185 23.744 241.105 23.672L242.337 23.576V23.464C242.337 22.808 241.945 22.576 241.417 22.576C240.785 22.576 240.425 22.856 240.425 23.328H239.769C239.769 22.512 240.441 21.976 241.449 21.976C242.417 21.976 243.073 22.488 243.073 23.544V26H242.433L242.353 25.368C242.153 25.816 241.617 26.096 240.985 26.096ZM241.201 25.512C241.913 25.512 242.345 25.048 242.345 24.312V24.104L241.345 24.184C240.681 24.248 240.409 24.504 240.409 24.888C240.409 25.304 240.713 25.512 241.201 25.512ZM243.866 23.944C243.866 22.84 244.578 21.976 245.714 21.976C246.346 21.976 246.834 22.264 247.082 22.752L247.138 22.088H247.81V25.84C247.81 27.072 247.05 27.856 245.85 27.856C244.786 27.856 244.058 27.256 243.914 26.264H244.666C244.762 26.84 245.194 27.176 245.85 27.176C246.586 27.176 247.066 26.696 247.066 25.952V25.168C246.81 25.624 246.298 25.896 245.682 25.896C244.57 25.896 243.866 25.04 243.866 23.944ZM244.618 23.928C244.618 24.664 245.074 25.24 245.81 25.24C246.57 25.24 247.034 24.696 247.034 23.928C247.034 23.176 246.586 22.632 245.818 22.632C245.066 22.632 244.618 23.208 244.618 23.928ZM250.584 26.096C249.432 26.096 248.64 25.264 248.64 24.048C248.64 22.824 249.416 21.976 250.552 21.976C251.664 21.976 252.392 22.744 252.392 23.904V24.184L249.368 24.192C249.424 25.016 249.856 25.472 250.6 25.472C251.184 25.472 251.568 25.232 251.696 24.784H252.4C252.208 25.624 251.552 26.096 250.584 26.096ZM250.552 22.608C249.896 22.608 249.48 23 249.384 23.688H251.64C251.64 23.04 251.216 22.608 250.552 22.608ZM253.984 26H253.232V22.088H253.912L253.992 22.688C254.24 22.24 254.736 21.976 255.288 21.976C256.32 21.976 256.792 22.608 256.792 23.608V26H256.04V23.776C256.04 22.984 255.672 22.664 255.096 22.664C254.392 22.664 253.984 23.168 253.984 23.928V26ZM257.577 24.048C257.577 22.824 258.353 21.976 259.497 21.976C260.481 21.976 261.169 22.536 261.305 23.408H260.553C260.425 22.912 260.025 22.648 259.521 22.648C258.809 22.648 258.321 23.192 258.321 24.04C258.321 24.88 258.777 25.424 259.489 25.424C260.025 25.424 260.425 25.144 260.561 24.68H261.313C261.161 25.528 260.433 26.096 259.489 26.096C258.337 26.096 257.577 25.28 257.577 24.048ZM261.652 27.752V27.136H262.156C262.516 27.136 262.804 27.08 262.988 26.584L263.124 26.208L261.548 22.088H262.34L263.476 25.24L264.636 22.088H265.412L263.564 26.88C263.3 27.552 262.908 27.832 262.308 27.832C262.06 27.832 261.852 27.8 261.652 27.752ZM265.909 26.096C265.597 26.096 265.333 25.84 265.333 25.536C265.333 25.224 265.597 24.968 265.909 24.968C266.213 24.968 266.477 25.224 266.477 25.536C266.477 25.84 266.213 26.096 265.909 26.096ZM267.061 24.048C267.061 22.824 267.837 21.976 268.981 21.976C269.965 21.976 270.653 22.536 270.789 23.408H270.037C269.909 22.912 269.509 22.648 269.005 22.648C268.293 22.648 267.805 23.192 267.805 24.04C267.805 24.88 268.261 25.424 268.973 25.424C269.509 25.424 269.909 25.144 270.045 24.68H270.797C270.645 25.528 269.917 26.096 268.973 26.096C267.821 26.096 267.061 25.28 267.061 24.048ZM271.335 24.04C271.335 22.832 272.199 21.984 273.375 21.984C274.543 21.984 275.407 22.832 275.407 24.04C275.407 25.248 274.543 26.096 273.375 26.096C272.199 26.096 271.335 25.248 271.335 24.04ZM272.095 24.04C272.095 24.856 272.615 25.424 273.375 25.424C274.127 25.424 274.655 24.856 274.655 24.04C274.655 23.224 274.127 22.656 273.375 22.656C272.615 22.656 272.095 23.224 272.095 24.04ZM276.991 26H276.239V22.088H276.911L276.983 22.6C277.183 22.232 277.599 21.976 278.151 21.976C278.759 21.976 279.191 22.28 279.391 22.768C279.575 22.28 280.047 21.976 280.655 21.976C281.551 21.976 282.095 22.536 282.095 23.44V26H281.359V23.624C281.359 23.008 281.015 22.656 280.487 22.656C279.919 22.656 279.543 23.056 279.543 23.68V26H278.799V23.616C278.799 23 278.463 22.664 277.935 22.664C277.367 22.664 276.991 23.056 276.991 23.68V26Z" fill="white"/>
<foreignObject x="17" y="46" width="375" height="199"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_2_130_668_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="15" x="32" y="61" width="345" height="169" rx="12" fill="white" fill-opacity="0.25"/>
<foreignObject x="17" y="227" width="375" height="49"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_3_130_668_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="15" x="32" y="242" width="345" height="19" rx="9.5" fill="white" fill-opacity="0.25"/>
<foreignObject x="17" y="258" width="289" height="42"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_4_130_668_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="15" x="32" y="273" width="259" height="12" rx="6" fill="white" fill-opacity="0.25"/>
<foreignObject x="17" y="278" width="254" height="42"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7.5px);clip-path:url(#bgblur_5_130_668_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="15" x="32" y="293" width="224" height="12" rx="6" fill="white" fill-opacity="0.25"/>
<defs>
<clipPath id="bgblur_0_130_668_clip_path" transform="translate(14.75 14.75)"><path d="M1 49C1 26.3726 1 15.0589 8.02944 8.02944C15.0589 1 26.3726 1 49 1H415C437.627 1 448.941 1 455.971 8.02944C463 15.0589 463 26.3726 463 49V291C463 313.627 463 324.941 455.971 331.971C448.941 339 437.627 339 415 339H49C26.3726 339 15.0589 339 8.02944 331.971C1 324.941 1 313.627 1 291V49Z"/>
</clipPath><filter id="filter1_d_130_668" x="31.502" y="17" width="12.2024" height="12.2024" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.297619" dy="0.297619"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_130_668"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_130_668" result="shape"/>
</filter>
<filter id="filter2_d_130_668" x="50.5493" y="17" width="12.2024" height="12.2024" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.297619" dy="0.297619"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_130_668"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_130_668" result="shape"/>
</filter>
<filter id="filter3_d_130_668" x="69.5972" y="17" width="12.2024" height="12.2024" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.297619" dy="0.297619"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_130_668"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_130_668" result="shape"/>
</filter>
<clipPath id="bgblur_1_130_668_clip_path" transform="translate(-85 2)"><rect x="100" y="13" width="278" height="20" rx="10"/>
</clipPath><clipPath id="bgblur_2_130_668_clip_path" transform="translate(-17 -46)"><rect x="32" y="61" width="345" height="169" rx="12"/>
</clipPath><clipPath id="bgblur_3_130_668_clip_path" transform="translate(-17 -227)"><rect x="32" y="242" width="345" height="19" rx="9.5"/>
</clipPath><clipPath id="bgblur_4_130_668_clip_path" transform="translate(-17 -258)"><rect x="32" y="273" width="259" height="12" rx="6"/>
</clipPath><clipPath id="bgblur_5_130_668_clip_path" transform="translate(-17 -278)"><rect x="32" y="293" width="224" height="12" rx="6"/>
</clipPath><linearGradient id="paint0_linear_130_668" x1="16.5019" y1="10.5" x2="316.502" y2="310.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#722973"/>
<stop offset="0.575256" stop-color="#722973" stop-opacity="0.111467"/>
<stop offset="1" stop-color="#722973" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_130_668" x1="14.0019" y1="11" x2="122.502" y2="170" gradientUnits="userSpaceOnUse">
<stop stop-color="#722973"/>
<stop offset="1" stop-color="#722973" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
